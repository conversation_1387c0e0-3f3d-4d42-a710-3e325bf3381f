"""
Test script để kiểm tra tính năng online document
"""

import requests
import json

# Test data
test_request = {
    "lesson_id": "lesson_01_01",
    "mon_hoc": "<PERSON><PERSON>a học",
    "lop": 12,
    "tong_so_cau": 5,
    "cau_hinh_de": [
        {
            "loai_cau_hoi": "trac_nghiem",
            "so_luong": 3,
            "muc_do": "nhan_biet",
            "noi_dung": "<PERSON>h<PERSON><PERSON> niệm cơ bản về hóa học"
        },
        {
            "loai_cau_hoi": "trac_nghiem", 
            "so_luong": 2,
            "muc_do": "thong_hieu",
            "noi_dung": "Ứng dụng kiến thức hóa học"
        }
    ]
}

def test_online_exam_generation():
    """Test endpoint tạo đề thi online"""
    print("Testing online exam generation...")
    
    url = "http://localhost:8000/api/v1/exam/generate-exam"
    
    try:
        response = requests.post(url, json=test_request, timeout=60)
        
        print(f"Status Code: {response.status_code}")
        print(f"Headers: {dict(response.headers)}")
        
        if response.status_code == 200:
            try:
                data = response.json()
                print("✅ Response JSON:")
                print(json.dumps(data, indent=2, ensure_ascii=False))
                
                # Kiểm tra response structure
                if data.get("success"):
                    print("SUCCESS: Online document created successfully!")
                    print(f"Primary link: {data.get('primary_link')}")
                    print(f"View link: {data.get('links', {}).get('view')}")
                    print(f"Edit link: {data.get('links', {}).get('edit')}")
                else:
                    print("WARNING: Online document creation failed, checking fallback...")
                    print(f"Error: {data.get('error')}")
                    if data.get('fallback_available'):
                        print("Fallback to download available")
                        
            except json.JSONDecodeError:
                print("ERROR: Response is not JSON, might be file download")
                print(f"Content-Type: {response.headers.get('content-type')}")
                if 'application/vnd.openxmlformats-officedocument' in response.headers.get('content-type', ''):
                    print("Received DOCX file (fallback mode)")

        else:
            print(f"ERROR: Request failed: {response.status_code}")
            try:
                error_data = response.json()
                print(f"Response: {json.dumps(error_data, indent=2, ensure_ascii=False)}")
            except:
                print(f"Response text length: {len(response.text)}")

    except requests.exceptions.RequestException as e:
        print(f"ERROR: Request error: {e}")

def test_download_exam_generation():
    """Test endpoint tạo đề thi download"""
    print("\nTesting download exam generation (backup endpoint)...")
    
    url = "http://localhost:8000/api/v1/exam/generate-exam-download"
    
    try:
        response = requests.post(url, json=test_request, timeout=60)
        
        print(f"Status Code: {response.status_code}")
        print(f"Content-Type: {response.headers.get('content-type')}")
        
        if response.status_code == 200:
            if 'application/vnd.openxmlformats-officedocument' in response.headers.get('content-type', ''):
                print("SUCCESS: DOCX file received successfully!")
                print(f"Content-Disposition: {response.headers.get('content-disposition')}")
                print(f"File size: {len(response.content)} bytes")
            else:
                print("ERROR: Unexpected content type")
        else:
            print(f"ERROR: Request failed: {response.status_code}")
            try:
                error_data = response.json()
                print(f"Response: {json.dumps(error_data, indent=2, ensure_ascii=False)}")
            except:
                print(f"Response text length: {len(response.text)}")

    except requests.exceptions.RequestException as e:
        print(f"ERROR: Request error: {e}")

def check_google_drive_status():
    """Kiểm tra trạng thái Google Drive service"""
    print("\nChecking Google Drive service status...")
    
    # Kiểm tra config
    import os
    from dotenv import load_dotenv
    load_dotenv()
    
    enable_gd = os.getenv("ENABLE_GOOGLE_DRIVE", "false").lower() == "true"
    credentials_path = os.getenv("GOOGLE_DRIVE_CREDENTIALS_PATH")
    
    print(f"ENABLE_GOOGLE_DRIVE: {enable_gd}")
    print(f"GOOGLE_DRIVE_CREDENTIALS_PATH: {credentials_path}")
    
    if credentials_path and os.path.exists(credentials_path):
        print("SUCCESS: Credentials file exists")
    else:
        print("WARNING: Credentials file not found")
        print("INFO: Google Drive will be disabled, API will fallback to file download")

if __name__ == "__main__":
    print("Testing PlanBook AI Online Document Feature")
    print("=" * 50)
    
    # Kiểm tra Google Drive status trước
    check_google_drive_status()
    
    # Test online generation
    test_online_exam_generation()
    
    # Test download generation
    test_download_exam_generation()
    
    print("\nTest completed!")
