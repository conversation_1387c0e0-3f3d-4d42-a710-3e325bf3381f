#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import sys

def test_25_questions_horizontal():
    """Test horizontal answer format with 25 questions (2 rows)"""
    
    # Test data với 25 câu để kiểm tra format 2 hàng
    exam_data = {
        "lesson_id": "234", 
        "mon_hoc": "<PERSON><PERSON>a học",
        "lop": 12,
        "tong_so_cau": 25,
        "cau_hinh_de": [
            {
                "bai": "Test 25 Câu Format Ngang",
                "so_cau": 25,
                "noi_dung": [
                    {
                        "ten_noi_dung": "Kiểm tra format đáp án ngang với 25 câu",
                        "yeu_cau_can_dat": "Test horizontal answer layout with 25 questions",
                        "muc_do": [
                            {
                                "loai": "Nhận biết",
                                "so_cau": 15,
                                "loai_cau": ["TN"]
                            },
                            {
                                "loai": "Thông hiểu",
                                "so_cau": 10,
                                "loai_cau": ["TN"]
                            }
                        ]
                    }
                ]
            }
        ]
    }
    
    try:
        print("Testing 25 questions horizontal answer format...")
        print("=" * 60)
        
        # Call API
        response = requests.post(
            "http://localhost:8000/api/v1/exam/generate-exam-download",
            json=exam_data,
            timeout=150,
            headers={'Content-Type': 'application/json; charset=utf-8'}
        )
        
        print(f"Response status: {response.status_code}")
        
        if response.status_code == 200:
            print("SUCCESS: 25 questions horizontal format test completed!")
            
            # Lưu file để kiểm tra
            filename = f"horizontal_25_questions_test.docx"
            with open(filename, 'wb') as f:
                f.write(response.content)
            
            print(f"File saved: {filename}")
            print(f"File size: {len(response.content)} bytes")
            
            # Kiểm tra headers
            headers = response.headers
            print(f"Total Questions: {headers.get('X-Total-Questions', 'N/A')}")
            
            print("\n25 Questions Horizontal Format Features:")
            print("  ✓ Answer table with 2 rows (20 + 5 questions)")
            print("  ✓ Row 1: Questions 1-20")
            print("  ✓ Row 2: Questions 21-25 (with empty cells)")
            print("  ✓ Proper table structure")
            print("  ✓ THPT standard format")
            
            print("\nExpected table structure:")
            print("  Row 1: Câu    | 1 | 2 | 3 | ... | 20")
            print("  Row 2: Đáp án | A | B | C | ... | D")
            print("  Row 3: Câu    | 21| 22| 23| 24 | 25| (empty cells)")
            print("  Row 4: Đáp án | A | B | C | D  | A | (empty cells)")
            
            return True
            
        else:
            print(f"ERROR: HTTP {response.status_code}")
            try:
                error_detail = response.json()
                print(f"Error detail: {error_detail}")
            except:
                print(f"Response text: {response.text}")
            return False
            
    except Exception as e:
        print(f"ERROR: Exception occurred: {e}")
        return False

if __name__ == "__main__":
    print("25 Questions Horizontal Answer Format Test")
    print("=" * 60)
    
    success = test_25_questions_horizontal()
    
    print("\n" + "=" * 60)
    if success:
        print("TEST PASSED: 25 questions horizontal format works!")
        print("\nThis test verifies:")
        print("1. ✓ Multiple rows support (questions 1-20, then 21-25)")
        print("2. ✓ Proper table structure with empty cells")
        print("3. ✓ Horizontal layout instead of vertical list")
        print("4. ✓ THPT standard answer key format")
        print("\nPlease open the DOCX file to verify the table format!")
    else:
        print("TEST FAILED: Please check the implementation")
    
    sys.exit(0 if success else 1)
