#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import sys

def test_debug_missing_answers():
    """Test to debug missing answer options"""
    
    # Test data với 2 câu để debug
    exam_data = {
        "lesson_id": "234", 
        "mon_hoc": "<PERSON><PERSON><PERSON> học",
        "lop": 12,
        "tong_so_cau": 2,
        "cau_hinh_de": [
            {
                "bai": "Debug Missing Answers",
                "so_cau": 2,
                "noi_dung": [
                    {
                        "ten_noi_dung": "Debug tại sao không có đáp án A, B, C, D",
                        "yeu_cau_can_dat": "Debug missing answer options",
                        "muc_do": [
                            {
                                "loai": "Nhận biết",
                                "so_cau": 2,
                                "loai_cau": ["TN"]
                            }
                        ]
                    }
                ]
            }
        ]
    }
    
    try:
        print("Testing to debug missing answer options...")
        print("=" * 60)
        
        # Call API
        response = requests.post(
            "http://localhost:8000/api/v1/exam/generate-exam-download",
            json=exam_data,
            timeout=120,
            headers={'Content-Type': 'application/json; charset=utf-8'}
        )
        
        print(f"Response status: {response.status_code}")
        
        if response.status_code == 200:
            print("SUCCESS: Debug test completed!")
            
            # Lưu file để kiểm tra
            filename = f"debug_missing_answers_test.docx"
            with open(filename, 'wb') as f:
                f.write(response.content)
            
            print(f"File saved: {filename}")
            print(f"File size: {len(response.content)} bytes")
            
            return True
            
        else:
            print(f"ERROR: HTTP {response.status_code}")
            try:
                error_detail = response.json()
                print(f"Error detail: {error_detail}")
            except:
                print(f"Response text: {response.text}")
            return False
            
    except Exception as e:
        print(f"ERROR: Exception occurred: {e}")
        return False

if __name__ == "__main__":
    print("Debug Missing Answer Options Test")
    print("=" * 60)
    
    success = test_debug_missing_answers()
    
    print("\n" + "=" * 60)
    if success:
        print("DEBUG TEST COMPLETED")
        print("Check server logs for detailed error information")
        print("Look for lines starting with 'Creating multiple choice answers with data:'")
    else:
        print("DEBUG TEST FAILED")
    
    sys.exit(0 if success else 1)
