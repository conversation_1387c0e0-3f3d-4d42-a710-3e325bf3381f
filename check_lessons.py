#!/usr/bin/env python3
"""
Script để kiểm tra lessons có sẵn trong database
"""

import asyncio
from motor.motor_asyncio import AsyncIOMotorClient
from app.core.config import settings

async def check_lessons():
    """Kiểm tra lessons trong database"""
    try:
        # Kết nối MongoDB
        client = AsyncIOMotorClient(settings.MONGODB_URL)
        db = client[settings.MONGODB_DATABASE]
        
        # Kiểm tra collections
        collections = await db.list_collection_names()
        print('📁 Collections:', collections)
        
        # Kiểm tra lessons
        if 'lessons' in collections:
            lessons = await db.lessons.find({}).limit(5).to_list(length=5)
            print('\n📚 Sample lessons:')
            for lesson in lessons:
                print(f'  - {lesson.get("_id")}: {lesson.get("title", "No title")}')
        
        # Kiểm tra textbooks
        if 'textbooks' in collections:
            textbooks = await db.textbooks.find({}).limit(3).to_list(length=3)
            print('\n📖 Sample textbooks:')
            for book in textbooks:
                print(f'  - {book.get("_id")}: {book.get("title", "No title")}')
                if 'chapters' in book:
                    for chapter in book['chapters'][:2]:
                        print(f'    📄 Chapter: {chapter.get("title", "No title")}')
                        if 'lessons' in chapter:
                            for lesson in chapter['lessons'][:2]:
                                lesson_id = lesson.get("lesson_id")
                                lesson_title = lesson.get("title", "No title")
                                print(f'      📝 Lesson: {lesson_id}: {lesson_title}')
                                
                                # Return first valid lesson_id for testing
                                if lesson_id:
                                    print(f'\n✅ Found valid lesson_id for testing: {lesson_id}')
                                    return lesson_id
        
        print('\n❌ No valid lesson_id found')
        return None
        
    except Exception as e:
        print(f'❌ Error: {e}')
        return None

if __name__ == "__main__":
    lesson_id = asyncio.run(check_lessons())
    if lesson_id:
        print(f'\n🎯 Use this lesson_id for testing: {lesson_id}')
