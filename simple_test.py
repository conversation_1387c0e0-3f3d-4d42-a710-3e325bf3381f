"""
Simple test để kiểm tra API
"""

import requests
import json

# Test với request đúng format
test_request = {
    "lesson_id": "lesson_01_01",
    "mon_hoc": "Hoa hoc",
    "lop": 12,
    "tong_so_cau": 2,
    "cau_hinh_de": [
        {
            "bai": "Bai 1: Khai niem co ban",
            "so_cau": 2,
            "noi_dung": [
                {
                    "ten_noi_dung": "Khai niem co ban ve hoa hoc",
                    "yeu_cau_can_dat": "Hieu duoc khai niem co ban",
                    "muc_do": [
                        {
                            "loai": "Nhận biết",
                            "so_cau": 2,
                            "loai_cau": ["TN"]
                        }
                    ]
                }
            ]
        }
    ]
}

def test_simple():
    print("Testing simple request...")
    
    url = "http://localhost:8000/api/v1/exam/generate-exam"
    
    try:
        print("Sending request...")
        response = requests.post(url, json=test_request, timeout=120)
        
        print(f"Status Code: {response.status_code}")
        print(f"Content-Type: {response.headers.get('content-type')}")
        
        if response.status_code == 422:
            try:
                error_data = response.json()
                print("Validation Error:")
                print(json.dumps(error_data, indent=2))
            except:
                print("Cannot parse error response")
        elif response.status_code == 200:
            print("SUCCESS!")
            if 'application/vnd.openxmlformats-officedocument' in response.headers.get('content-type', ''):
                print(f"DOCX file received, size: {len(response.content)} bytes")
            else:
                print("Unexpected content type")
        else:
            print(f"Error: {response.status_code}")
            
    except Exception as e:
        print(f"Request error: {e}")

if __name__ == "__main__":
    test_simple()
