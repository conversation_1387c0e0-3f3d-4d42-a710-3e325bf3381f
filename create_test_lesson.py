#!/usr/bin/env python3
"""
Script để tạo lesson test trong database
"""

import asyncio
from motor.motor_asyncio import AsyncIOMotorClient
from app.core.config import settings
from datetime import datetime

async def create_test_lesson():
    """Tạo lesson test trong database"""
    try:
        # Kết nối MongoDB
        client = AsyncIOMotorClient(settings.MONGODB_URL)
        db = client[settings.MONGODB_DATABASE]
        
        # Tạo test textbook với lesson
        test_textbook = {
            "_id": "test_book_001",
            "title": "Hóa học 12 - Test Book",
            "subject": "<PERSON><PERSON>a học",
            "grade": 12,
            "publisher": "Test Publisher",
            "year": 2024,
            "created_at": datetime.now(),
            "chapters": [
                {
                    "chapter_id": "test_chapter_001",
                    "title": "Chương 1: Cấu tạo nguyên tử",
                    "chapter_number": 1,
                    "lessons": [
                        {
                            "lesson_id": "test_lesson_001",
                            "title": "Bài 1: <PERSON>ấu tạo nguyên tử",
                            "lesson_number": 1,
                            "content": """
                            Nguyên tử là đơn vị cơ bản của vật chất. <PERSON>uyên tử gồm có:
                            
                            1. Hạt nhân nguyên tử:
                            - Proton: mang điện tích dương (+1)
                            - Neutron: không mang điện tích (0)
                            
                            2. Vỏ electron:
                            - Electron: mang điện tích âm (-1)
                            - Chuyển động xung quanh hạt nhân
                            
                            Số proton = Số electron trong nguyên tử trung hòa
                            Số khối A = Số proton + Số neutron
                            
                            Ví dụ: Nguyên tử Carbon (C):
                            - Số proton: 6
                            - Số neutron: 6  
                            - Số electron: 6
                            - Số khối: 12
                            """,
                            "objectives": [
                                "Hiểu được cấu tạo nguyên tử",
                                "Phân biệt được proton, neutron, electron",
                                "Tính được số khối nguyên tử"
                            ],
                            "keywords": ["nguyên tử", "proton", "neutron", "electron", "hạt nhân", "số khối"]
                        }
                    ]
                }
            ]
        }
        
        # Insert hoặc update textbook
        await db.textbooks.replace_one(
            {"_id": "test_book_001"}, 
            test_textbook, 
            upsert=True
        )
        
        print("✅ Test textbook created successfully!")
        print(f"📚 Book ID: test_book_001")
        print(f"📝 Lesson ID: test_lesson_001")
        print(f"📖 Title: {test_textbook['title']}")
        
        # Verify lesson exists
        book = await db.textbooks.find_one({"_id": "test_book_001"})
        if book and book.get("chapters"):
            lesson = book["chapters"][0]["lessons"][0]
            print(f"✅ Lesson verified: {lesson['lesson_id']} - {lesson['title']}")
            return lesson["lesson_id"]
        
        return None
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return None

if __name__ == "__main__":
    lesson_id = asyncio.run(create_test_lesson())
    if lesson_id:
        print(f"\n🎯 Use this lesson_id for testing: {lesson_id}")
