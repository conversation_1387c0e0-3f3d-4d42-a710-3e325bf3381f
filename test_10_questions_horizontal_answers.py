#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import sys

def test_10_questions_horizontal_answers():
    """Test horizontal answer format with 10 questions"""
    
    # Test data với 10 câu
    exam_data = {
        "lesson_id": "234", 
        "mon_hoc": "<PERSON><PERSON><PERSON> học",
        "lop": 12,
        "tong_so_cau": 10,
        "cau_hinh_de": [
            {
                "bai": "Test 10 Câu Đáp Án <PERSON>",
                "so_cau": 10,
                "noi_dung": [
                    {
                        "ten_noi_dung": "Kiểm tra format đáp án hàng ngang với 10 câu",
                        "yeu_cau_can_dat": "Test horizontal answer layout with 10 questions",
                        "muc_do": [
                            {
                                "loai": "Nhận biết",
                                "so_cau": 6,
                                "loai_cau": ["TN"]
                            },
                            {
                                "loai": "Thông hiểu",
                                "so_cau": 4,
                                "loai_cau": ["TN"]
                            }
                        ]
                    }
                ]
            }
        ]
    }
    
    try:
        print("Testing 10 questions with horizontal answer format...")
        print("=" * 70)
        
        # Call API
        response = requests.post(
            "http://localhost:8000/api/v1/exam/generate-exam-download",
            json=exam_data,
            timeout=150,
            headers={'Content-Type': 'application/json; charset=utf-8'}
        )
        
        print(f"Response status: {response.status_code}")
        
        if response.status_code == 200:
            print("SUCCESS: 10 questions horizontal answers test completed!")
            
            # Lưu file để kiểm tra
            filename = f"horizontal_10_questions_answers_test.docx"
            with open(filename, 'wb') as f:
                f.write(response.content)
            
            print(f"File saved: {filename}")
            print(f"File size: {len(response.content)} bytes")
            
            # Kiểm tra headers
            headers = response.headers
            print(f"Total Questions: {headers.get('X-Total-Questions', 'N/A')}")
            
            print("\n10 Questions Horizontal Answer Format:")
            print("  ✓ All 10 questions use horizontal answer layout")
            print("  ✓ Each question: 2x2 table for A, B, C, D")
            print("  ✓ Better readability and space usage")
            print("  ✓ Consistent format throughout exam")
            print("  ✓ Answer key still in single horizontal row")
            
            print("\nFormat for each question:")
            print("  Câu X: Question text...")
            print("  | A. Answer 1    | B. Answer 2    |")
            print("  | C. Answer 3    | D. Answer 4    |")
            print("  (Repeated for all 10 questions)")
            
            return True
            
        else:
            print(f"ERROR: HTTP {response.status_code}")
            try:
                error_detail = response.json()
                print(f"Error detail: {error_detail}")
            except:
                print(f"Response text: {response.text}")
            return False
            
    except Exception as e:
        print(f"ERROR: Exception occurred: {e}")
        return False

if __name__ == "__main__":
    print("10 Questions Horizontal Answers Format Test")
    print("=" * 70)
    
    success = test_10_questions_horizontal_answers()
    
    print("\n" + "=" * 70)
    if success:
        print("TEST PASSED: 10 questions horizontal answers format works!")
        print("\nThis confirms:")
        print("1. ✓ All questions use horizontal answer layout")
        print("2. ✓ Consistent 2x2 table format for each question")
        print("3. ✓ Better space utilization")
        print("4. ✓ More readable than vertical list")
        print("5. ✓ Answer key remains in single horizontal row")
        print("\nPlease check the DOCX file to verify the format!")
    else:
        print("TEST FAILED: Please check the implementation")
    
    sys.exit(0 if success else 1)
