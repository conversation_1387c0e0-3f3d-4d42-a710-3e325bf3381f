#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import sys

def test_single_row_answer_format():
    """Test single row horizontal answer format"""
    
    # Test data với 5 câu để dễ kiểm tra format
    exam_data = {
        "lesson_id": "234", 
        "mon_hoc": "<PERSON><PERSON>a học",
        "lop": 12,
        "tong_so_cau": 5,
        "cau_hinh_de": [
            {
                "bai": "Test Format 1 Hàng Ngang",
                "so_cau": 5,
                "noi_dung": [
                    {
                        "ten_noi_dung": "Kiểm tra format đáp án 1 hàng ngang",
                        "yeu_cau_can_dat": "Test single row horizontal answer layout",
                        "muc_do": [
                            {
                                "loai": "Nhận biết",
                                "so_cau": 5,
                                "loai_cau": ["TN"]
                            }
                        ]
                    }
                ]
            }
        ]
    }
    
    try:
        print("Testing single row horizontal answer format...")
        print("=" * 60)
        
        # Call API
        response = requests.post(
            "http://localhost:8000/api/v1/exam/generate-exam-download",
            json=exam_data,
            timeout=120,
            headers={'Content-Type': 'application/json; charset=utf-8'}
        )
        
        print(f"Response status: {response.status_code}")
        
        if response.status_code == 200:
            print("SUCCESS: Single row answer format test completed!")
            
            # Lưu file để kiểm tra
            filename = f"single_row_answers_test.docx"
            with open(filename, 'wb') as f:
                f.write(response.content)
            
            print(f"File saved: {filename}")
            print(f"File size: {len(response.content)} bytes")
            
            # Kiểm tra headers
            headers = response.headers
            print(f"Total Questions: {headers.get('X-Total-Questions', 'N/A')}")
            
            print("\nSingle Row Answer Format Features:")
            print("  ✓ All answers in ONE single row")
            print("  ✓ Format: | Câu | 1 | 2 | 3 | 4 | 5 |")
            print("  ✓ Format: | Đáp án | A | B | C | D | A |")
            print("  ✓ No multiple rows - everything in 1 table")
            print("  ✓ Proper THPT exam structure")
            
            print("\nExpected table structure:")
            print("  Row 1: Câu    | 1 | 2 | 3 | 4 | 5")
            print("  Row 2: Đáp án | A | B | C | D | A")
            print("  (All in ONE horizontal table)")
            
            return True
            
        else:
            print(f"ERROR: HTTP {response.status_code}")
            try:
                error_detail = response.json()
                print(f"Error detail: {error_detail}")
            except:
                print(f"Response text: {response.text}")
            return False
            
    except Exception as e:
        print(f"ERROR: Exception occurred: {e}")
        return False

if __name__ == "__main__":
    print("Single Row Horizontal Answer Format Test")
    print("=" * 60)
    
    success = test_single_row_answer_format()
    
    print("\n" + "=" * 60)
    if success:
        print("TEST PASSED: Single row horizontal format implemented!")
        print("\nChanges made:")
        print("1. ✓ Changed from multiple rows to SINGLE row")
        print("2. ✓ All answers in one horizontal table")
        print("3. ✓ Format: | Câu | 1 | 2 | 3 | 4 | 5 |")
        print("4. ✓ Format: | Đáp án | A | B | C | D | A |")
        print("5. ✓ No matter how many questions - all in 1 row")
        print("\nPlease check the generated DOCX file!")
    else:
        print("TEST FAILED: Please check the implementation")
        print("Note: If API quota exceeded, wait or use new API key")
    
    sys.exit(0 if success else 1)
