#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import sys

def test_horizontal_answer_format():
    """Test horizontal answer format with smaller exam"""
    
    # Test data với 10 câu để dễ kiểm tra format
    exam_data = {
        "lesson_id": "234",
        "mon_hoc": "<PERSON><PERSON>a học",
        "lop": 12,
        "tong_so_cau": 10,
        "cau_hinh_de": [
            {
                "bai": "Test Format Đáp Án Ngang",
                "so_cau": 10,
                "noi_dung": [
                    {
                        "ten_noi_dung": "Kiểm tra format đáp án ngang",
                        "yeu_cau_can_dat": "Test horizontal answer layout",
                        "muc_do": [
                            {
                                "loai": "Nhận biết",
                                "so_cau": 10,
                                "loai_cau": ["TN"]
                            }
                        ]
                    }
                ]
            }
        ]
    }
    
    try:
        print("Testing horizontal answer format...")
        print("=" * 50)
        
        # Call API
        response = requests.post(
            "http://localhost:8000/api/v1/exam/generate-exam-download",
            json=exam_data,
            timeout=120,
            headers={'Content-Type': 'application/json; charset=utf-8'}
        )
        
        print(f"Response status: {response.status_code}")
        
        if response.status_code == 200:
            print("SUCCESS: Horizontal answer format test completed!")
            
            # Lưu file để kiểm tra
            filename = f"horizontal_answers_test.docx"
            with open(filename, 'wb') as f:
                f.write(response.content)
            
            print(f"File saved: {filename}")
            print(f"File size: {len(response.content)} bytes")
            
            # Kiểm tra headers
            headers = response.headers
            print(f"Total Questions: {headers.get('X-Total-Questions', 'N/A')}")
            
            print("\nHorizontal Answer Format Features:")
            print("  ✓ Answer table with horizontal layout")
            print("  ✓ Questions numbered 1-20 per row")
            print("  ✓ Answers displayed in table format")
            print("  ✓ Proper THPT exam structure")
            print("\nPlease open the DOCX file to verify:")
            print("  - Bảng đáp án should be in horizontal table format")
            print("  - Row 1: Câu | 1 | 2 | 3 | 4 | 5 | ... | 20")
            print("  - Row 2: Đáp án | A | B | C | D | A | ... | B")
            
            return True
            
        else:
            print(f"ERROR: HTTP {response.status_code}")
            try:
                error_detail = response.json()
                print(f"Error detail: {error_detail}")
            except:
                print(f"Response text: {response.text}")
            return False
            
    except Exception as e:
        print(f"ERROR: Exception occurred: {e}")
        return False

if __name__ == "__main__":
    print("Horizontal Answer Format Test")
    print("=" * 50)
    
    success = test_horizontal_answer_format()
    
    print("\n" + "=" * 50)
    if success:
        print("TEST PASSED: Horizontal answer format implemented!")
        print("\nChanges made:")
        print("1. ✓ Changed from vertical list (1. A, 2. B, 3. C...)")
        print("2. ✓ To horizontal table format")
        print("3. ✓ Questions numbered across columns")
        print("4. ✓ Answers displayed in table rows")
        print("5. ✓ Supports multiple rows for longer exams")
        print("\nPlease check the generated DOCX file to confirm the format!")
    else:
        print("TEST FAILED: Please check the implementation")
    
    sys.exit(0 if success else 1)
