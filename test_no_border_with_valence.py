#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import sys

def test_no_border_with_valence():
    """Test no border answers with chemistry valence table"""
    
    # Test data với môn Hóa học để có bảng hóa trị
    exam_data = {
        "lesson_id": "234", 
        "mon_hoc": "<PERSON>óa học",  # Quan trọng: để có bảng hóa trị
        "lop": 12,
        "tong_so_cau": 5,
        "cau_hinh_de": [
            {
                "bai": "Test Không Viền + Bảng Hóa Trị",
                "so_cau": 5,
                "noi_dung": [
                    {
                        "ten_noi_dung": "Kiểm tra format đáp án không viền và bảng hóa trị",
                        "yeu_cau_can_dat": "Test no border answers with valence table",
                        "muc_do": [
                            {
                                "loai": "Nhận biết",
                                "so_cau": 5,
                                "loai_cau": ["TN"]
                            }
                        ]
                    }
                ]
            }
        ]
    }
    
    try:
        print("Testing no border answers with chemistry valence table...")
        print("=" * 70)
        
        # Call API
        response = requests.post(
            "http://localhost:8000/api/v1/exam/generate-exam-download",
            json=exam_data,
            timeout=120,
            headers={'Content-Type': 'application/json; charset=utf-8'}
        )
        
        print(f"Response status: {response.status_code}")
        
        if response.status_code == 200:
            print("SUCCESS: No border answers with valence table test completed!")
            
            # Lưu file để kiểm tra
            filename = f"no_border_with_valence_test.docx"
            with open(filename, 'wb') as f:
                f.write(response.content)
            
            print(f"File saved: {filename}")
            print(f"File size: {len(response.content)} bytes")
            
            # Kiểm tra headers
            headers = response.headers
            print(f"Total Questions: {headers.get('X-Total-Questions', 'N/A')}")
            
            print("\nNo Border + Valence Table Features:")
            print("  ✓ Chemistry valence table at top")
            print("  ✓ Question answers without table borders")
            print("  ✓ Clean horizontal layout for answers")
            print("  ✓ Format: A. Answer1    B. Answer2")
            print("  ✓ Format: C. Answer3    D. Answer4")
            print("  ✓ No visible table lines around answers")
            print("  ✓ Proper THPT exam structure")
            
            print("\nExpected features:")
            print("  1. Valence table: H = 1, C = 12, N = 14, O = 16...")
            print("  2. Each question answers in clean horizontal format")
            print("  3. No table borders around A, B, C, D options")
            print("  4. Better visual appearance")
            print("  5. Answer key still in single horizontal row")
            
            return True
            
        else:
            print(f"ERROR: HTTP {response.status_code}")
            try:
                error_detail = response.json()
                print(f"Error detail: {error_detail}")
            except:
                print(f"Response text: {response.text}")
            return False
            
    except Exception as e:
        print(f"ERROR: Exception occurred: {e}")
        return False

if __name__ == "__main__":
    print("No Border Answers + Chemistry Valence Table Test")
    print("=" * 70)
    
    success = test_no_border_with_valence()
    
    print("\n" + "=" * 70)
    if success:
        print("TEST PASSED: No border answers with valence table implemented!")
        print("\nChanges made:")
        print("1. ✓ Added chemistry valence table at top of exam")
        print("2. ✓ Removed table borders from question answers")
        print("3. ✓ Clean horizontal layout for A, B, C, D options")
        print("4. ✓ Better visual appearance without grid lines")
        print("5. ✓ Maintains THPT exam standards")
        print("\nPlease check the generated DOCX file to verify!")
    else:
        print("TEST FAILED: Please check the implementation")
    
    sys.exit(0 if success else 1)
