#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import sys

def test_horizontal_question_answers():
    """Test horizontal answer format for individual questions"""
    
    # Test data với 3 câu để dễ kiểm tra format
    exam_data = {
        "lesson_id": "234", 
        "mon_hoc": "<PERSON><PERSON><PERSON> học",
        "lop": 12,
        "tong_so_cau": 3,
        "cau_hinh_de": [
            {
                "bai": "Test Đáp Án Hàng Ngang Từng Câu",
                "so_cau": 3,
                "noi_dung": [
                    {
                        "ten_noi_dung": "Kiểm tra format đáp án hàng ngang cho từng câu hỏi",
                        "yeu_cau_can_dat": "Test horizontal answer layout for each question",
                        "muc_do": [
                            {
                                "loai": "Nhận biết",
                                "so_cau": 3,
                                "loai_cau": ["TN"]
                            }
                        ]
                    }
                ]
            }
        ]
    }
    
    try:
        print("Testing horizontal answer format for individual questions...")
        print("=" * 70)
        
        # Call API
        response = requests.post(
            "http://localhost:8000/api/v1/exam/generate-exam-download",
            json=exam_data,
            timeout=120,
            headers={'Content-Type': 'application/json; charset=utf-8'}
        )
        
        print(f"Response status: {response.status_code}")
        
        if response.status_code == 200:
            print("SUCCESS: Horizontal question answers format test completed!")
            
            # Lưu file để kiểm tra
            filename = f"horizontal_question_answers_test.docx"
            with open(filename, 'wb') as f:
                f.write(response.content)
            
            print(f"File saved: {filename}")
            print(f"File size: {len(response.content)} bytes")
            
            # Kiểm tra headers
            headers = response.headers
            print(f"Total Questions: {headers.get('X-Total-Questions', 'N/A')}")
            
            print("\nHorizontal Question Answer Format Features:")
            print("  ✓ Each question's answers in horizontal table (2x2)")
            print("  ✓ Format: A. Answer1    B. Answer2")
            print("  ✓ Format: C. Answer3    D. Answer4")
            print("  ✓ No vertical list for individual question answers")
            print("  ✓ Proper THPT exam structure")
            
            print("\nExpected format for each question:")
            print("  Câu X: Question text here...")
            print("  +------------------+------------------+")
            print("  | A. Answer 1      | B. Answer 2      |")
            print("  +------------------+------------------+")
            print("  | C. Answer 3      | D. Answer 4      |")
            print("  +------------------+------------------+")
            print("  (Instead of vertical A, B, C, D list)")
            
            return True
            
        else:
            print(f"ERROR: HTTP {response.status_code}")
            try:
                error_detail = response.json()
                print(f"Error detail: {error_detail}")
            except:
                print(f"Response text: {response.text}")
            return False
            
    except Exception as e:
        print(f"ERROR: Exception occurred: {e}")
        return False

if __name__ == "__main__":
    print("Horizontal Question Answers Format Test")
    print("=" * 70)
    
    success = test_horizontal_question_answers()
    
    print("\n" + "=" * 70)
    if success:
        print("TEST PASSED: Horizontal question answers format implemented!")
        print("\nChanges made:")
        print("1. ✓ Changed individual question answers from vertical to horizontal")
        print("2. ✓ Each question uses 2x2 table for A, B, C, D answers")
        print("3. ✓ Format: A. Answer1    B. Answer2")
        print("4. ✓ Format: C. Answer3    D. Answer4")
        print("5. ✓ Better space utilization and readability")
        print("\nPlease check the generated DOCX file to verify!")
    else:
        print("TEST FAILED: Please check the implementation")
    
    sys.exit(0 if success else 1)
