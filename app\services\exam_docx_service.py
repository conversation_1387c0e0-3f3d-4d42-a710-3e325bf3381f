"""
Service để xuất đề thi ra file DOCX
"""

import logging
import os
import re
import tempfile
from typing import Dict, List, Any, Optional
from docx import Document
from docx.shared import Inches, Pt
from docx.enum.text import WD_ALIGN_PARAGRAPH

from docx.oxml.shared import OxmlElement, qn
from datetime import datetime

logger = logging.getLogger(__name__)


class ExamDocxService:
    """Service để tạo file DOCX cho đề thi"""

    def __init__(self):
        # Sử dụng thư mục tạm thời của hệ thống
        self.temp_dir = tempfile.gettempdir()

    async def create_exam_docx(
        self, exam_data: Dict[str, Any], exam_request: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Tạo file DOCX cho đề thi

        Args:
            exam_data: Dữ liệu đề thi đã tạo
            exam_request: Thông tin request gốc

        Returns:
            Dict chứa thông tin file đã tạo
        """
        try:
            # Tạo document mới
            doc = Document()

            # Thiết lập margins và font
            self._setup_document_style(doc)

            # Tạo header đề thi
            self._create_exam_header(doc, exam_request, exam_data)

            # Tạo thông tin đề thi
            self._create_exam_info(doc, exam_request, exam_data)

            # Tạo câu hỏi
            self._create_questions_section(doc, exam_data.get("questions", []))

            # Tạo đáp án (trang riêng)
            self._create_answer_key(doc, exam_data.get("questions", []))

            # Lưu file với filename an toàn (không có ký tự đặc biệt) trong thư mục tạm thời
            lesson_id_safe = self._sanitize_filename(
                exam_request.get("lesson_id", "unknown")
            )
            filename = (
                f"exam_{lesson_id_safe}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.docx"
            )
            filepath = os.path.join(self.temp_dir, filename)

            doc.save(filepath)

            return {
                "success": True,
                "filename": filename,
                "filepath": filepath,
                "file_size": os.path.getsize(filepath),
                "created_at": datetime.now().isoformat(),
            }

        except Exception as e:
            logger.error(f"Error creating exam DOCX: {e}")
            return {"success": False, "error": str(e)}

    def _sanitize_filename(self, filename: str) -> str:
        """
        Làm sạch filename để tránh lỗi encoding

        Args:
            filename: Tên file gốc

        Returns:
            Tên file đã được làm sạch (chỉ chứa ASCII)
        """
        try:
            # Loại bỏ ký tự đặc biệt và dấu tiếng Việt
            # Chỉ giữ lại chữ cái, số, dấu gạch dưới và gạch ngang
            sanitized = re.sub(r"[^\w\-_]", "_", filename)

            # Loại bỏ nhiều dấu gạch dưới liên tiếp
            sanitized = re.sub(r"_+", "_", sanitized)

            # Loại bỏ dấu gạch dưới ở đầu và cuối
            sanitized = sanitized.strip("_")

            # Nếu kết quả rỗng, dùng default
            if not sanitized:
                sanitized = "lesson"

            return sanitized

        except Exception as e:
            logger.warning(f"Error sanitizing filename '{filename}': {e}")
            return "lesson"

    def _setup_document_style(self, doc: Document):
        """Thiết lập style cho document"""
        try:
            # Thiết lập margins
            sections = doc.sections
            for section in sections:
                section.top_margin = Inches(0.8)
                section.bottom_margin = Inches(0.8)
                section.left_margin = Inches(0.8)
                section.right_margin = Inches(0.8)

            # Thiết lập font mặc định
            style = doc.styles["Normal"]
            font = style.font
            font.name = "Times New Roman"
            font.size = Pt(12)

        except Exception as e:
            logger.error(f"Error setting up document style: {e}")

    def _create_exam_header(
        self, doc: Document, exam_request: Dict[str, Any], exam_data: Dict[str, Any]
    ):
        """Tạo header cho đề thi theo chuẩn THPT"""
        try:
            # Tạo bảng header 2 cột
            header_table = doc.add_table(rows=1, cols=2)
            header_table.autofit = False
            header_table.columns[0].width = Inches(3.0)
            header_table.columns[1].width = Inches(3.5)

            # Cột trái - Thông tin trường/sở
            left_cell = header_table.cell(0, 0)
            left_para = left_cell.paragraphs[0]
            left_para.alignment = WD_ALIGN_PARAGRAPH.CENTER

            # Logo/Tên cơ quan
            left_para.add_run("BỘ GIÁO DỤC VÀ ĐÀO TạO\n").bold = True
            left_para.add_run("TRƯỜNG THPT ABC\n").bold = True
            left_para.add_run("(Đề có ... trang)")

            # Cột phải - Thông tin đề thi
            right_cell = header_table.cell(0, 1)
            right_para = right_cell.paragraphs[0]
            right_para.alignment = WD_ALIGN_PARAGRAPH.CENTER

            # Loại đề thi
            exam_type = "ĐỀ KIỂM TRA"
            if exam_request.get('tong_so_cau', 0) >= 40:
                exam_type = "ĐỀ THI"

            right_para.add_run(f"{exam_type} LỚP {exam_request.get('lop', 12)}\n").bold = True
            right_para.add_run(f"Môn: {exam_request.get('mon_hoc', '').upper()}\n").bold = True

            # Thời gian làm bài
            total_questions = exam_request.get('tong_so_cau', 0)
            time_minutes = 45 if total_questions <= 20 else 50 if total_questions <= 30 else 90
            right_para.add_run(f"Thời gian làm bài: {time_minutes} phút, không kể thời gian phát đề")

            # Xóa border của bảng
            for row in header_table.rows:
                for cell in row.cells:
                    cell._element.get_or_add_tcPr().append(
                        OxmlElement('w:tcBorders')
                    )

            # Khoảng trống
            doc.add_paragraph()

            # Đường kẻ ngang
            separator = doc.add_paragraph()
            separator.alignment = WD_ALIGN_PARAGRAPH.CENTER
            separator.add_run("─" * 80)

            doc.add_paragraph()

        except Exception as e:
            logger.error(f"Error creating exam header: {e}")

    def _create_exam_info(
        self, doc: Document, exam_request: Dict[str, Any], exam_data: Dict[str, Any]
    ):
        """Tạo thông tin đề thi theo chuẩn THPT"""
        try:
            # Thông tin học sinh theo format chuẩn THPT
            info_para = doc.add_paragraph()
            info_para.add_run("Họ, tên thí sinh: ").bold = True
            info_para.add_run("." * 50)

            # Số báo danh
            sbd_para = doc.add_paragraph()
            sbd_para.add_run("Số báo danh: ").bold = True
            sbd_para.add_run("." * 55)

            # Thống kê đề thi theo chuẩn THPT
            stats = exam_data.get("statistics", {})
            total_questions = stats.get('tong_so_cau', 0)

            # Phần I - Câu trắc nghiệm nhiều phương án lựa chọn
            tn_count = stats.get("TN", 0)
            if tn_count > 0:
                part1_para = doc.add_paragraph()
                part1_run = part1_para.add_run(
                    f"PHẦN I. Câu trắc nghiệm nhiều phương án lựa chọn. "
                    f"Thí sinh trả lời từ câu 1 đến câu {tn_count}. "
                    f"Mỗi câu hỏi thí sinh chỉ chọn một phương án."
                )
                part1_run.font.name = "Times New Roman"
                part1_run.font.size = Pt(11)
                part1_run.bold = True

            # Phần II - Câu đúng/sai (nếu có)
            ds_count = stats.get("DS", 0)
            if ds_count > 0:
                part2_para = doc.add_paragraph()
                start_num = tn_count + 1
                end_num = tn_count + ds_count
                part2_run = part2_para.add_run(
                    f"PHẦN II. Câu trắc nghiệm đúng sai. "
                    f"Thí sinh trả lời từ câu {start_num} đến câu {end_num}. "
                    f"Trong mỗi ý a), b), c), d) ở mỗi câu, thí sinh chọn đúng hoặc sai."
                )
                part2_run.font.name = "Times New Roman"
                part2_run.font.size = Pt(11)
                part2_run.bold = True

            # Phần III - Câu trả lời ngắn/tự luận (nếu có)
            tl_count = stats.get("TL", 0)
            dt_count = stats.get("DT", 0)
            part3_count = tl_count + dt_count
            if part3_count > 0:
                part3_para = doc.add_paragraph()
                start_num = tn_count + ds_count + 1
                end_num = total_questions
                part3_run = part3_para.add_run(
                    f"PHẦN III. Câu trả lời ngắn. "
                    f"Thí sinh trả lời từ câu {start_num} đến câu {end_num}."
                )
                part3_run.font.name = "Times New Roman"
                part3_run.font.size = Pt(11)
                part3_run.bold = True

            doc.add_paragraph()  # Khoảng trống

            # Đường kẻ phân cách
            separator = doc.add_paragraph()
            separator.alignment = WD_ALIGN_PARAGRAPH.CENTER
            separator.add_run("─" * 80)

            doc.add_paragraph()

        except Exception as e:
            logger.error(f"Error creating exam info: {e}")

    def _create_questions_section(self, doc: Document, questions: List[Dict[str, Any]]):
        """Tạo phần câu hỏi"""
        try:
            # Tiêu đề phần câu hỏi
            questions_title = doc.add_heading("PHẦN I: CÂU HỎI", level=2)
            questions_title_run = questions_title.runs[0]
            questions_title_run.font.name = "Times New Roman"
            questions_title_run.font.size = Pt(14)

            # Tạo từng câu hỏi
            for i, question in enumerate(questions, 1):
                self._create_single_question(doc, question, i)

        except Exception as e:
            logger.error(f"Error creating questions section: {e}")

    def _create_single_question(
        self, doc: Document, question: Dict[str, Any], question_num: int
    ):
        """Tạo một câu hỏi"""
        try:
            loai_cau = question.get("loai_cau", "")

            # Số thứ tự và nội dung câu hỏi
            q_para = doc.add_paragraph()
            q_para.add_run(f"Câu {question_num}: ").bold = True
            q_para.add_run(question.get("noi_dung_cau_hoi", ""))

            # Tạo đáp án theo loại câu hỏi
            if loai_cau == "TN":
                self._create_multiple_choice_answers(doc, question.get("dap_an", {}))
            elif loai_cau == "DT":
                self._create_fill_blank_answer(doc)
            elif loai_cau == "DS":
                self._create_true_false_answers(doc, question.get("dap_an", {}))
            elif loai_cau == "TL":
                self._create_essay_answer_space(doc)

            doc.add_paragraph()  # Khoảng trống giữa các câu

        except Exception as e:
            logger.error(f"Error creating single question: {e}")

    def _create_multiple_choice_answers(self, doc: Document, dap_an: Dict[str, Any]):
        """Tạo đáp án trắc nghiệm theo hàng ngang"""
        try:
            options = ["A", "B", "C", "D"]
            available_options = []

            # Lấy các đáp án có sẵn
            for option in options:
                if option in dap_an:
                    available_options.append((option, dap_an[option]))

            if not available_options:
                return

            # Tạo bảng 2x2 cho 4 đáp án (2 hàng, 2 cột)
            if len(available_options) <= 4:
                table = doc.add_table(rows=2, cols=2)
                table.style = 'Table Grid'

                # Thiết lập độ rộng cột
                for col in table.columns:
                    col.width = Inches(3.0)

                # Điền đáp án vào bảng
                for i, (option, text) in enumerate(available_options):
                    row_idx = i // 2  # 0 hoặc 1
                    col_idx = i % 2   # 0 hoặc 1

                    if row_idx < 2 and col_idx < 2:
                        cell = table.cell(row_idx, col_idx)
                        cell_para = cell.paragraphs[0]
                        cell_para.add_run(f"{option}. ").bold = True
                        cell_para.add_run(text)

                        # Thiết lập font
                        for run in cell_para.runs:
                            run.font.name = "Times New Roman"
                            run.font.size = Pt(12)

            # Thêm khoảng trống sau bảng đáp án
            doc.add_paragraph()

        except Exception as e:
            logger.error(f"Error creating multiple choice answers: {e}")

    def _create_fill_blank_answer(self, doc: Document):
        """Tạo chỗ trống cho câu điền từ"""
        try:
            answer_para = doc.add_paragraph()
            answer_para.add_run("   Đáp án: ")
            answer_para.add_run("_" * 30)

        except Exception as e:
            logger.error(f"Error creating fill blank answer: {e}")

    def _create_true_false_answers(self, doc: Document, dap_an: Dict[str, Any]):
        """Tạo đáp án đúng/sai"""
        try:
            options = ["a", "b", "c", "d"]
            for option in options:
                option_para = doc.add_paragraph()
                option_para.add_run(f"   {option}) ").bold = True
                option_para.add_run("Đúng ☐    Sai ☐")

        except Exception as e:
            logger.error(f"Error creating true/false answers: {e}")

    def _create_essay_answer_space(self, doc: Document):
        """Tạo chỗ trống cho câu tự luận"""
        try:
            # Thêm dòng kẻ cho học sinh viết
            for i in range(5):
                line_para = doc.add_paragraph()
                line_para.add_run("_" * 80)

        except Exception as e:
            logger.error(f"Error creating essay answer space: {e}")

    def _create_answer_key(self, doc: Document, questions: List[Dict[str, Any]]):
        """Tạo đáp án (trang riêng)"""
        try:
            # Ngắt trang
            doc.add_page_break()

            # Tiêu đề đáp án
            answer_title = doc.add_heading("ĐÁP ÁN VÀ HƯỚNG DẪN CHẤM", level=2)
            answer_title_run = answer_title.runs[0]
            answer_title_run.font.name = "Times New Roman"
            answer_title_run.font.size = Pt(14)

            # Tạo bảng đáp án cho câu trắc nghiệm
            tn_questions = [q for q in questions if q.get("loai_cau") == "TN"]
            if tn_questions:
                self._create_answer_table(doc, tn_questions)

            # Đáp án chi tiết cho các loại câu khác
            other_questions = [q for q in questions if q.get("loai_cau") != "TN"]
            if other_questions:
                doc.add_paragraph()
                detail_title = doc.add_heading("Đáp án chi tiết:", level=3)
                detail_title_run = detail_title.runs[0]
                detail_title_run.font.name = "Times New Roman"
                detail_title_run.font.size = Pt(12)

                for question in other_questions:
                    question_num = question.get("stt", 1)  # Sử dụng số thứ tự thực tế từ dữ liệu
                    self._create_detailed_answer(doc, question, question_num)

        except Exception as e:
            logger.error(f"Error creating answer key: {e}")

    def _create_answer_table(self, doc: Document, tn_questions: List[Dict[str, Any]]):
        """Tạo bảng đáp án trắc nghiệm theo format 1 hàng ngang"""
        try:
            if not tn_questions:
                return

            # Tiêu đề đáp án theo chuẩn THPT
            doc.add_paragraph()
            answer_list_title = doc.add_heading("BẢNG ĐÁP ÁN", level=3)
            answer_list_title.alignment = WD_ALIGN_PARAGRAPH.CENTER
            answer_list_title_run = answer_list_title.runs[0]
            answer_list_title_run.font.name = "Times New Roman"
            answer_list_title_run.font.size = Pt(14)
            answer_list_title_run.bold = True

            # Tạo format 1 hàng ngang: 1.A  2.B  3.C  4.D  5.A...
            total_questions = len(tn_questions)

            # Tạo bảng với 2 hàng: header và đáp án
            # Số cột = số câu hỏi + 1 (cột đầu cho label)
            table = doc.add_table(rows=2, cols=total_questions + 1)
            table.style = 'Table Grid'

            # Thiết lập độ rộng cột
            for col in table.columns:
                col.width = Inches(0.5)

            # Hàng 1: Header với số câu
            header_row = table.rows[0]
            header_row.cells[0].text = "Câu"
            header_row.cells[0].paragraphs[0].runs[0].font.bold = True
            header_row.cells[0].paragraphs[0].alignment = WD_ALIGN_PARAGRAPH.CENTER

            for i in range(total_questions):
                header_row.cells[i + 1].text = str(i + 1)
                header_row.cells[i + 1].paragraphs[0].runs[0].font.bold = True
                header_row.cells[i + 1].paragraphs[0].alignment = WD_ALIGN_PARAGRAPH.CENTER

            # Hàng 2: Đáp án
            answer_row = table.rows[1]
            answer_row.cells[0].text = "Đáp án"
            answer_row.cells[0].paragraphs[0].runs[0].font.bold = True
            answer_row.cells[0].paragraphs[0].alignment = WD_ALIGN_PARAGRAPH.CENTER

            # Điền đáp án cho từng câu
            for i, question in enumerate(tn_questions):
                dap_an = question.get("dap_an", {})
                correct_answer = dap_an.get("dung", "")

                # Nếu không có đáp án đúng, thử trích xuất từ giải thích
                if not correct_answer:
                    giai_thich = question.get("giai_thich", "")
                    correct_answer = self._extract_correct_answer_from_explanation(giai_thich, dap_an)
                    if not correct_answer:
                        correct_answer = "?"

                answer_row.cells[i + 1].text = correct_answer
                answer_row.cells[i + 1].paragraphs[0].runs[0].font.bold = True
                answer_row.cells[i + 1].paragraphs[0].alignment = WD_ALIGN_PARAGRAPH.CENTER

            # Thêm giải thích chi tiết cho từng câu
            doc.add_paragraph()
            detail_title = doc.add_heading("Giải thích chi tiết:", level=3)
            detail_title_run = detail_title.runs[0]
            detail_title_run.font.name = "Times New Roman"
            detail_title_run.font.size = Pt(12)

            for i, question in enumerate(tn_questions, 1):
                # Câu hỏi và đáp án đúng
                answer_para = doc.add_paragraph()
                answer_para.add_run(f"Câu {i}: ").bold = True

                dap_an = question.get("dap_an", {})
                correct_answer = dap_an.get("dung", "")
                answer_para.add_run(f"Đáp án: {correct_answer}")

                # Giải thích
                giai_thich = question.get("giai_thich", "")
                if giai_thich:
                    explain_para = doc.add_paragraph()
                    explain_para.add_run("Giải thích: ").italic = True
                    explain_para.add_run(giai_thich)

                doc.add_paragraph()  # Khoảng trống

        except Exception as e:
            logger.error(f"Error creating answer list: {e}")

    def _create_multi_column_answer_table(self, doc: Document, tn_questions: List[Dict[str, Any]], answers_per_column: int):
        """Tạo bảng đáp án nhiều cột"""
        try:
            total_questions = len(tn_questions)
            num_columns = (total_questions + answers_per_column - 1) // answers_per_column

            # Tạo bảng với số cột phù hợp
            table = doc.add_table(rows=answers_per_column, cols=num_columns)
            table.autofit = False

            # Thiết lập độ rộng cột
            for col in table.columns:
                col.width = Inches(1.5)

            # Điền đáp án vào bảng
            for i, question in enumerate(tn_questions):
                row_idx = i % answers_per_column
                col_idx = i // answers_per_column

                if col_idx < num_columns and row_idx < answers_per_column:
                    cell = table.cell(row_idx, col_idx)
                    cell_para = cell.paragraphs[0]

                    dap_an = question.get("dap_an", {})
                    correct_answer = dap_an.get("dung", "")

                    if not correct_answer:
                        giai_thich = question.get("giai_thich", "")
                        correct_answer = self._extract_correct_answer_from_explanation(giai_thich, dap_an)
                        if not correct_answer:
                            correct_answer = "?"

                    cell_para.add_run(f"{i+1}. ").bold = True
                    cell_para.add_run(correct_answer).bold = True

            # Xóa border của bảng để trông gọn gàng hơn
            for row in table.rows:
                for cell in row.cells:
                    cell._element.get_or_add_tcPr().append(OxmlElement('w:tcBorders'))

        except Exception as e:
            logger.error(f"Error creating multi-column answer table: {e}")

    def _create_detailed_answer(
        self, doc: Document, question: Dict[str, Any], question_num: int
    ):
        """Tạo đáp án chi tiết"""
        try:
            # Số câu
            answer_para = doc.add_paragraph()
            answer_para.add_run(f"Câu {question_num}: ").bold = True

            # Đáp án theo loại
            loai_cau = question.get("loai_cau", "")
            dap_an = question.get("dap_an", {})

            if loai_cau == "DT":
                answer_para.add_run(dap_an.get("dap_an_chinh", ""))
            elif loai_cau == "DS":
                ds_answers = []
                for key in ["a", "b", "c", "d"]:
                    if key in dap_an:
                        status = "Đúng" if dap_an[key] else "Sai"
                        ds_answers.append(f"{key}) {status}")
                answer_para.add_run("; ".join(ds_answers))
            elif loai_cau == "TL":
                y_chinh = dap_an.get("y_chinh", [])
                if y_chinh:
                    answer_para.add_run("\n".join([f"- {y}" for y in y_chinh]))

            # Giải thích
            giai_thich = question.get("giai_thich", "")
            if giai_thich:
                explain_para = doc.add_paragraph()
                explain_para.add_run("Giải thích: ").italic = True
                explain_para.add_run(giai_thich)

            doc.add_paragraph()  # Khoảng trống

        except Exception as e:
            logger.error(f"Error creating detailed answer: {e}")

    def _get_question_type_name(self, loai_cau: str) -> str:
        """Lấy tên đầy đủ của loại câu hỏi"""
        names = {
            "TN": "Trắc nghiệm",
            "DT": "Điền từ",
            "DS": "Đúng/Sai",
            "TL": "Tự luận",
        }
        return names.get(loai_cau, loai_cau)

    def _extract_correct_answer_from_explanation(self, explanation: str, dap_an: dict) -> str:
        """Trích xuất đáp án đúng từ giải thích (copy từ exam_generation_service)"""
        try:
            import re

            if not explanation or not isinstance(dap_an, dict):
                return ""

            explanation_lower = explanation.lower()

            # Tìm các pattern rõ ràng nhất trước
            strong_patterns = [
                r"đáp án ([abcd]) đúng",
                r"đáp án đúng là ([abcd])",
                r"([abcd]) đúng vì",
                r"([abcd]) là đáp án đúng",
                r"([abcd]) đúng",
                r"chọn đáp án ([abcd])",
                r"đáp án:\s*([abcd])",
                r"đáp án\s+([abcd])"
            ]

            for pattern in strong_patterns:
                match = re.search(pattern, explanation_lower)
                if match:
                    answer = match.group(1).upper()
                    if answer in dap_an:
                        return answer

            # Tìm pattern yếu hơn
            weak_patterns = [
                r"đáp án ([abcd])",
                r"chọn ([abcd])",
                r"([abcd])\s*[:\-\.]",
                r"^([abcd])\s"
            ]

            for pattern in weak_patterns:
                match = re.search(pattern, explanation_lower)
                if match:
                    answer = match.group(1).upper()
                    if answer in dap_an:
                        return answer

            # Phân tích nội dung đáp án
            option_scores = {}
            for option, content in dap_an.items():
                if option in ['A', 'B', 'C', 'D'] and isinstance(content, str):
                    content_words = content.lower().split()
                    score = 0
                    for word in content_words:
                        if len(word) > 2 and word in explanation_lower:
                            score += 1
                    option_scores[option] = score

            if option_scores:
                best_option = max(option_scores.keys(), key=lambda x: option_scores[x])
                if option_scores[best_option] > 0:
                    return best_option

            return ""

        except Exception as e:
            logger.error(f"Error extracting correct answer: {e}")
            return ""


# Tạo instance global
exam_docx_service = ExamDocxService()
