#!/usr/bin/env python3
"""
Test script để kiểm tra endpoint generate-exam trả về link online
"""

import requests
import json

def test_generate_exam_online():
    """Test endpoint generate-exam với response online"""
    
    url = "http://localhost:8000/api/v1/exam/generate-exam"
    
    # Request data mẫu
    request_data = {
        "lesson_id": "test_lesson_001",
        "mon_hoc": "<PERSON>óa học",
        "lop": 12,
        "tong_so_cau": 10,
        "cau_hinh_de": [
            {
                "bai": "Bài 1: Cấu tạo nguyên tử",
                "so_cau": 10,
                "noi_dung": [
                    {
                        "ten_noi_dung": "Cấu tạo nguyên tử",
                        "yeu_cau_can_dat": "Hi<PERSON>u được cấu tạo nguyên tử, electron, proton, neutron",
                        "muc_do": [
                            {
                                "loai": "Nhận biết",
                                "so_cau": 4,
                                "loai_cau": ["TN"]
                            },
                            {
                                "loai": "Thông hiểu",
                                "so_cau": 4,
                                "loai_cau": ["TN"]
                            },
                            {
                                "loai": "Vận dụng",
                                "so_cau": 2,
                                "loai_cau": ["TN"]
                            }
                        ]
                    }
                ]
            }
        ]
    }
    
    print("🚀 Testing generate-exam endpoint...")
    print(f"URL: {url}")
    print(f"Request data: {json.dumps(request_data, indent=2, ensure_ascii=False)}")
    
    try:
        response = requests.post(url, json=request_data, timeout=60)
        
        print(f"\n📊 Response Status: {response.status_code}")
        print(f"📊 Response Headers: {dict(response.headers)}")
        
        if response.status_code == 200:
            try:
                data = response.json()
                print(f"\n✅ SUCCESS - Response data:")
                print(json.dumps(data, indent=2, ensure_ascii=False))
                
                # Kiểm tra các field quan trọng
                if data.get("success"):
                    print(f"\n🎉 Online document created successfully!")
                    print(f"📄 File ID: {data.get('file_id')}")
                    print(f"📄 Filename: {data.get('filename')}")
                    print(f"🔗 Primary Link: {data.get('primary_link')}")
                    
                    links = data.get("links", {})
                    if links:
                        print(f"\n🔗 Available Links:")
                        for link_type, link_url in links.items():
                            print(f"  - {link_type}: {link_url}")
                else:
                    print(f"\n❌ Failed to create online document: {data.get('error')}")
                    
            except json.JSONDecodeError:
                print(f"\n❌ Invalid JSON response")
                print(f"Raw response: {response.text[:500]}...")
                
        else:
            print(f"\n❌ HTTP Error: {response.status_code}")
            try:
                error_data = response.json()
                print(f"Error details: {json.dumps(error_data, indent=2, ensure_ascii=False)}")
            except:
                print(f"Raw error: {response.text}")
                
    except requests.exceptions.Timeout:
        print(f"\n⏰ Request timeout after 60 seconds")
    except requests.exceptions.ConnectionError:
        print(f"\n🔌 Connection error - Is the server running?")
    except Exception as e:
        print(f"\n💥 Unexpected error: {e}")

if __name__ == "__main__":
    test_generate_exam_online()
