#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import sys

def test_20_questions_single_row():
    """Test single row format with 20 questions"""
    
    # Test data với 20 câu
    exam_data = {
        "lesson_id": "234", 
        "mon_hoc": "<PERSON><PERSON><PERSON> học",
        "lop": 12,
        "tong_so_cau": 20,
        "cau_hinh_de": [
            {
                "bai": "Test 20 Câu Format 1 Hàng",
                "so_cau": 20,
                "noi_dung": [
                    {
                        "ten_noi_dung": "Kiểm tra format đáp án 1 hàng với 20 câu",
                        "yeu_cau_can_dat": "Test single row with 20 questions",
                        "muc_do": [
                            {
                                "loai": "Nhận biết",
                                "so_cau": 12,
                                "loai_cau": ["TN"]
                            },
                            {
                                "loai": "Thông hiểu",
                                "so_cau": 8,
                                "loai_cau": ["TN"]
                            }
                        ]
                    }
                ]
            }
        ]
    }
    
    try:
        print("Testing 20 questions in single row format...")
        print("=" * 60)
        
        # Call API
        response = requests.post(
            "http://localhost:8000/api/v1/exam/generate-exam-download",
            json=exam_data,
            timeout=150,
            headers={'Content-Type': 'application/json; charset=utf-8'}
        )
        
        print(f"Response status: {response.status_code}")
        
        if response.status_code == 200:
            print("SUCCESS: 20 questions single row format test completed!")
            
            # Lưu file để kiểm tra
            filename = f"single_row_20_questions_test.docx"
            with open(filename, 'wb') as f:
                f.write(response.content)
            
            print(f"File saved: {filename}")
            print(f"File size: {len(response.content)} bytes")
            
            # Kiểm tra headers
            headers = response.headers
            print(f"Total Questions: {headers.get('X-Total-Questions', 'N/A')}")
            
            print("\n20 Questions Single Row Format:")
            print("  ✓ All 20 answers in ONE single row")
            print("  ✓ Format: | Câu | 1 | 2 | 3 | ... | 20 |")
            print("  ✓ Format: | Đáp án | A | B | C | ... | D |")
            print("  ✓ Wide table but still single row")
            print("  ✓ No multiple rows - everything horizontal")
            
            print("\nExpected table structure:")
            print("  Row 1: Câu    | 1 | 2 | 3 | 4 | 5 | ... | 20")
            print("  Row 2: Đáp án | A | B | C | D | A | ... | D")
            print("  (All 20 questions in ONE horizontal table)")
            
            return True
            
        else:
            print(f"ERROR: HTTP {response.status_code}")
            try:
                error_detail = response.json()
                print(f"Error detail: {error_detail}")
            except:
                print(f"Response text: {response.text}")
            return False
            
    except Exception as e:
        print(f"ERROR: Exception occurred: {e}")
        return False

if __name__ == "__main__":
    print("20 Questions Single Row Format Test")
    print("=" * 60)
    
    success = test_20_questions_single_row()
    
    print("\n" + "=" * 60)
    if success:
        print("TEST PASSED: 20 questions single row format works!")
        print("\nThis confirms:")
        print("1. ✓ Single row format works with many questions")
        print("2. ✓ Table expands horizontally, not vertically")
        print("3. ✓ All answers in one continuous row")
        print("4. ✓ Format: | Câu | 1 | 2 | ... | 20 |")
        print("5. ✓ Format: | Đáp án | A | B | ... | D |")
        print("\nPlease check the DOCX file to verify!")
    else:
        print("TEST FAILED: Please check the implementation")
    
    sys.exit(0 if success else 1)
