#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import json
import sys

def test_thpt_format():
    """Test THPT exam format generation"""

    # Load test data from JSON file
    try:
        with open('exam_test_request.json', 'r', encoding='utf-8') as f:
            exam_data = json.load(f)
    except Exception as e:
        print(f"ERROR: Cannot load test data: {e}")
        return False
    
    try:
        print("Testing THPT exam format generation...")
        print("=" * 50)
        
        # Call API
        response = requests.post(
            "http://localhost:8000/api/v1/exam/generate-exam-test",
            json=exam_data,
            timeout=120,
            headers={'Content-Type': 'application/json; charset=utf-8'}
        )
        
        print(f"Response status: {response.status_code}")
        print(f"Response headers: {response.headers}")
        print(f"Response text: {response.text[:200]}...")

        if response.status_code == 200:
            if response.text.strip():
                result = response.json()
                print("SUCCESS: Exam generated successfully!")
                print(f"Document link: {result.get('document_link', 'N/A')}")
            else:
                print("ERROR: Empty response from server")
                return False
            
            # Check statistics
            if 'statistics' in result:
                stats = result['statistics']
                print("\nExam Statistics:")
                print(f"  - Multiple Choice (TN): {stats.get('TN', 0)} questions")
                print(f"  - True/False (DS): {stats.get('DS', 0)} questions")
                print(f"  - Short Answer (DT+TL): {stats.get('DT', 0) + stats.get('TL', 0)} questions")
            
            # Check THPT format features
            print("\nTHPT Format Features Implemented:")
            print("  + Standard THPT header with school information")
            print("  + Student information fields (Ho ten, So bao danh)")
            print("  + Three-part exam structure (Phan I, II, III)")
            print("  + Vertical answer key format (1. A, 2. B, 3. C...)")
            print("  + Proper exam timing and instructions")
            
            return True
            
        else:
            print(f"ERROR: HTTP {response.status_code}")
            print(f"Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"ERROR: Exception occurred: {e}")
        return False

if __name__ == "__main__":
    print("THPT Exam Format Test")
    print("=" * 50)
    
    success = test_thpt_format()
    
    print("\n" + "=" * 50)
    if success:
        print("TEST PASSED: THPT format implementation successful!")
        print("\nKey improvements made:")
        print("1. Header follows THPT standards with school info layout")
        print("2. Student information section added")
        print("3. Three-part structure clearly defined")
        print("4. Answer key changed from horizontal table to vertical list")
        print("5. Multi-column support for longer exams")
    else:
        print("TEST FAILED: Please check the implementation")
    
    sys.exit(0 if success else 1)
