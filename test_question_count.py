import requests

# Test tạo đề thi để kiểm tra số câu hỏi
url = "http://127.0.0.1:8000/api/v1/exam/generate-exam-download"

# Test với 10 câu trước
payload = {
    "lesson_id": "234", 
    "mon_hoc": "<PERSON><PERSON><PERSON> học",
    "lop": 12,
    "tong_so_cau": 10,
    "cau_hinh_de": [
        {
            "bai": "Cấu tạo nguyên tử",
            "so_cau": 10,
            "noi_dung": [
                {
                    "ten_noi_dung": "Hạt cấu tạo nguyên tử",
                    "yeu_cau_can_dat": "Học sinh hiểu và phân biệt được proton, neutron và electron về khối lư<PERSON>, đi<PERSON><PERSON> tích, vị trí trong nguyên tử.",
                    "muc_do": [
                        {
                            "loai": "Nhận biết",
                            "so_cau": 5,
                            "loai_cau": ["TN"]
                        },
                        {
                            "loai": "Thông hiểu",
                            "so_cau": 3,
                            "loai_cau": ["TN"]
                        },
                        {
                            "loai": "Vận dụng",
                            "so_cau": 2,
                            "loai_cau": ["TN"]
                        }
                    ]
                }
            ]
        }
    ]
}

headers = {
    "Content-Type": "application/json",
    "X-API-Key": "test-api-key-2024"
}

print("Đang test với 10 câu hỏi...")
response = requests.post(url, json=payload, headers=headers)

print(f"Status code: {response.status_code}")
if response.status_code == 200:
    print(f"Tạo đề thi thành công!")
    print(f"File size: {len(response.content)} bytes")
    
    # Lưu file để kiểm tra
    with open("test_10_questions_count.docx", "wb") as f:
        f.write(response.content)
    print("Đã lưu file test_10_questions_count.docx")
else:
    print(f"Lỗi: {response.text}")
