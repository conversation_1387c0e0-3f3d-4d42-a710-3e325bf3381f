# Hướng dẫn cấu hình Google Drive API

Để sử dụng tính năng trả về link doc online thay vì download file DOCX, bạn cần cấu hình Google Drive API.

## Bước 1: Tạo Google Cloud Project

1. T<PERSON>y cập [Google Cloud Console](https://console.cloud.google.com/)
2. Tạo project mới hoặc chọn project hiện có
3. Ghi nhớ Project ID

## Bước 2: Enable Google Drive API

1. Trong Google Cloud Console, vào **APIs & Services** > **Library**
2. Tìm kiếm "Google Drive API"
3. Click vào "Google Drive API" và nhấn **Enable**

## Bước 3: Tạo Service Account

1. Vào **APIs & Services** > **Credentials**
2. Click **Create Credentials** > **Service Account**
3. Điền thông tin:
   - Service account name: `planbook-drive-service`
   - Service account ID: `planbook-drive-service`
   - Description: `Service account for PlanBook AI Google Drive integration`
4. Click **Create and Continue**
5. Bỏ qua phần Grant access (optional)
6. Click **Done**

## Bước 4: Tạo và Download Key

1. Trong danh sách Service Accounts, click vào service account vừa tạo
2. Vào tab **Keys**
3. Click **Add Key** > **Create new key**
4. Chọn **JSON** format
5. Click **Create** - file JSON sẽ được download tự động
6. Lưu file này vào thư mục dự án với tên `google-drive-credentials.json`

## Bước 5: Tạo Google Drive Folder (Optional)

1. Truy cập [Google Drive](https://drive.google.com/)
2. Tạo folder mới để chứa các file từ PlanBook AI
3. Click chuột phải vào folder > **Share**
4. Thêm email của service account (có trong file JSON) với quyền **Editor**
5. Copy Folder ID từ URL (phần sau `/folders/`)

## Bước 6: Cấu hình Environment Variables

Cập nhật file `.env`:

```env
# Google Drive Configuration
ENABLE_GOOGLE_DRIVE=true
GOOGLE_DRIVE_CREDENTIALS_PATH=google-drive-credentials.json
GOOGLE_DRIVE_FOLDER_ID=your_folder_id_here
GOOGLE_DRIVE_AUTO_DELETE_DAYS=7
```

## Bước 7: Cài đặt Dependencies

```bash
pip install -r requirements.txt
```

## Bước 8: Test Configuration

Restart server và test endpoint:

```bash
fastapi dev app/main.py
```

Gọi API `/api/v1/exam/generate-exam` - nếu cấu hình đúng, response sẽ chứa links online thay vì file download.

## Troubleshooting

### Lỗi "Google Drive service not available"
- Kiểm tra file credentials có tồn tại không
- Kiểm tra đường dẫn trong `GOOGLE_DRIVE_CREDENTIALS_PATH`
- Kiểm tra `ENABLE_GOOGLE_DRIVE=true`

### Lỗi "Permission denied"
- Kiểm tra service account có quyền truy cập folder không
- Kiểm tra Google Drive API đã được enable chưa

### Lỗi "Quota exceeded"
- Google Drive API có giới hạn quota
- Kiểm tra usage trong Google Cloud Console

## Fallback Mode

Nếu Google Drive không hoạt động, hệ thống sẽ tự động fallback về mode download file DOCX như cũ.

Bạn cũng có thể sử dụng endpoint backup: `/api/v1/exam/generate-exam-download`
