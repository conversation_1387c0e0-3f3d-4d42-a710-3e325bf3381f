import requests
import json

# Test tạo đề thi với bảng đáp án theo cấu trúc THPT
url = "http://127.0.0.1:8000/api/v1/exam/generate-exam-download"

payload = {
    "lesson_id": "test_lesson",
    "mon_hoc": "<PERSON><PERSON><PERSON> học",
    "lop": "12",
    "loai_de": "Kiểm tra 15 phút",
    "do_kho": "Trung bình",
    "tong_so_cau": 5,
    "cau_hinh_de": [
        {
            "chuong": "Chương 1: Cấu tạo nguyên tử",
            "bai": "Bài 1: Thành phần nguyên tử",
            "noi_dung": "<PERSON>ấu tạo nguyên tử, số proton, neutron, electron",
            "so_cau": 5,
            "do_kho": "Trung bình",
            "loai_cau": ["TN"]
        }
    ]
}

headers = {
    "Content-Type": "application/json",
    "X-API-Key": "test-api-key-2024"
}

print("<PERSON><PERSON> gửi request tạo đề thi với bảng đáp án THPT...")
response = requests.post(url, json=payload, headers=headers)

print(f"Status code: {response.status_code}")
if response.status_code == 200:
    result = response.json()
    print(f"Tạo đề thi thành công!")
    print(f"File size: {len(response.content)} bytes")
    
    # Lưu file để kiểm tra
    with open("test_thpt_answer_format.docx", "wb") as f:
        f.write(response.content)
    print("Đã lưu file test_thpt_answer_format.docx")
else:
    print(f"Lỗi: {response.text}")
