import requests
import json

# Test tạo đề thi với bảng đáp án theo cấu trúc THPT
url = "http://127.0.0.1:8000/api/v1/exam/generate-exam-download"

payload = {
    "lesson_id": "234",
    "mon_hoc": "<PERSON><PERSON><PERSON> học",
    "lop": 12,
    "tong_so_cau": 60,
    "cau_hinh_de": [
        {
            "bai": "Cấu tạo nguyên tử",
            "so_cau": 60,
            "noi_dung": [
                {
                    "ten_noi_dung": "Hạt cấu tạo nguyên tử",
                    "yeu_cau_can_dat": "Học sinh hiểu và phân biệt được proton, neutron và electron về khối lượng, đi<PERSON><PERSON> tích, vị trí trong nguyên tử.",
                    "muc_do": [
                        {
                            "loai": "Nhận biết",
                            "so_cau": 25,
                            "loai_cau": ["TN"]
                        },
                        {
                            "loai": "Thông hiểu",
                            "so_cau": 20,
                            "loai_cau": ["TN"]
                        },
                        {
                            "loai": "Vận dụng",
                            "so_cau": 10,
                            "loai_cau": ["TN"]
                        },
                        {
                            "loai": "Vận dụng cao",
                            "so_cau": 5,
                            "loai_cau": ["TN"]
                        }
                    ]
                }
            ]
        }
    ]
}

headers = {
    "Content-Type": "application/json",
    "X-API-Key": "test-api-key-2024"
}

print("Đang gửi request tạo đề thi 60 câu với bảng đáp án THPT...")
print(f"Request payload: {payload}")
response = requests.post(url, json=payload, headers=headers)

print(f"Status code: {response.status_code}")
if response.status_code == 200:
    print(f"Tạo đề thi thành công!")
    print(f"File size: {len(response.content)} bytes")

    # Lưu file để kiểm tra
    with open("test_60_questions_thpt_format.docx", "wb") as f:
        f.write(response.content)
    print("Đã lưu file test_60_questions_thpt_format.docx")
    print("Hãy mở file để kiểm tra:")
    print("1. Có đủ 60 câu hỏi không")
    print("2. Bảng đáp án có đúng format THPT không")
else:
    print(f"Lỗi: {response.text}")
