"""
Test script để kiểm tra format đề thi mới theo chuẩn THPT
"""

import requests
import json

# URL của API
BASE_URL = "http://localhost:8000"

def test_exam_generation():
    """Test tạo đề thi với format mới"""
    
    # Ma trận đề thi mẫu theo chuẩn THPT
    exam_matrix = {
        "lesson_id": "test_lesson_chemistry",
        "mon_hoc": "Hoa hoc",
        "lop": 12,
        "tong_so_cau": 10,
        "cau_hinh_de": [
            {
                "bai": "Cau tao nguyen tu",
                "so_cau": 10,
                "noi_dung": [
                    {
                        "ten_noi_dung": "Cau tao nguyen tu va bang tuan hoan",
                        "muc_do": [
                            {
                                "loai": "Nhan biet",
                                "so_cau": 6,
                                "loai_cau": ["TN"]
                            },
                            {
                                "loai": "Thong hieu",
                                "so_cau": 3,
                                "loai_cau": ["TN"]
                            },
                            {
                                "loai": "Van dung",
                                "so_cau": 1,
                                "loai_cau": ["TN"]
                            }
                        ]
                    }
                ]
            }
        ]
    }
    
    print("Testing exam generation with new THPT format...")
    print(f"Exam matrix: {exam_matrix['mon_hoc']} - Lop {exam_matrix['lop']}")
    print(f"Total questions: {exam_matrix['tong_so_cau']}")

    try:
        # Gọi API tạo đề thi mock (không cần lesson_id thực)
        response = requests.post(
            f"{BASE_URL}/api/exam/generate-exam-mock",
            json=exam_matrix,
            timeout=60
        )

        if response.status_code == 200:
            result = response.json()
            print("SUCCESS: Exam generation successful!")
            print(f"Document link: {result.get('document_link', 'N/A')}")
            print(f"Statistics: {result.get('statistics', {})}")

            # Hiển thị thông tin về format mới
            if 'statistics' in result:
                stats = result['statistics']
                print("\nExam Structure (New THPT Format):")
                print(f"   - Phan I (Trac nghiem): {stats.get('TN', 0)} cau")
                print(f"   - Phan II (Dung/Sai): {stats.get('DS', 0)} cau")
                print(f"   - Phan III (Tra loi ngan): {stats.get('DT', 0) + stats.get('TL', 0)} cau")

            return True

        else:
            print(f"ERROR: {response.status_code}")
            print(f"Response: {response.text}")
            return False

    except Exception as e:
        print(f"ERROR: Exception occurred: {e}")
        return False

def test_answer_format():
    """Test format đáp án mới"""
    print("\nTesting new answer format...")
    print("Expected format:")
    print("   BANG DAP AN")
    print("   1. A")
    print("   2. B")
    print("   3. C")
    print("   ...")
    print("   (Vertical format like THPT standard)")

if __name__ == "__main__":
    print("Starting THPT Exam Format Test")
    print("=" * 50)

    # Test tạo đề thi
    success = test_exam_generation()

    # Test format đáp án
    test_answer_format()

    print("\n" + "=" * 50)
    if success:
        print("SUCCESS: Test completed successfully!")
        print("New THPT format features:")
        print("   + Standard THPT header with school info")
        print("   + Three-part exam structure (Phan I, II, III)")
        print("   + Vertical answer key format (1. A, 2. B, 3. C...)")
        print("   + Student info fields (Ho ten, So bao danh)")
    else:
        print("ERROR: Test failed!")
